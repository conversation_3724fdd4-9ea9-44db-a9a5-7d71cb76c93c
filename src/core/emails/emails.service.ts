import { Inject, Injectable, Logger } from '@nestjs/common';
import * as nodemailer from 'nodemailer';

import { ConfigService } from '@nestjs/config';
import { EMAIL } from './emails.module';

@Injectable()
export class EmailsService {
  private readonly logger = new Logger(EmailsService.name);

  constructor(
    readonly config: ConfigService,
    @Inject(EMAIL) private readonly transport: nodemailer.Transporter,
  ) {}

  async sendWelcomeEmail(userEmail: string, userName: string): Promise<void> {}
}
